<?xml version="1.0" encoding="utf-8"?>
<!-- vim: set sts=2 sw=2: -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <!-- simple types -->
  <xs:simpleType name="precision">
    <xs:restriction base="xs:string">
      <xs:enumeration value="empty"/>
      <xs:enumeration value="unanalyzed"/>
      <xs:enumeration value="precise"/>
      <xs:enumeration value="imprecise"/>
      <xs:enumeration value="any"/>
    </xs:restriction>
  </xs:simpleType>
  <!-- root elements -->
  <xs:element name="mypy-report-index">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="file" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:attribute name="name" type="xs:string" use="required"/>
            <xs:attribute name="module" type="xs:string" use="required"/>
            <xs:attribute name="total" type="xs:integer" use="required"/>
            <xs:attribute name="empty" type="xs:integer" use="required"/>
            <xs:attribute name="unanalyzed" type="xs:integer" use="required"/>
            <xs:attribute name="precise" type="xs:integer" use="required"/>
            <xs:attribute name="imprecise" type="xs:integer" use="required"/>
            <xs:attribute name="any" type="xs:integer" use="required"/>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="name" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>
  <xs:element name="mypy-report-file">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="line" minOccurs="0" maxOccurs="unbounded">
          <xs:complexType>
            <xs:attribute name="number" type="xs:integer" use="required"/>
            <xs:attribute name="precision" type="precision" use="required"/>
            <xs:attribute name="any_info" type="xs:string" use="optional"/>
            <xs:attribute name="content" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="name" type="xs:string" use="required"/>
      <xs:attribute name="module" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>
</xs:schema>
