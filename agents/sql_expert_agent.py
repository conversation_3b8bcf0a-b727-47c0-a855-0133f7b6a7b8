"""
SQL专家智能体
负责将自然语言转换为SQL查询语句
"""

import json
from typing import Dict, Any, List
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage

from agents.base_agent import BaseAgent, AgentMessage, MessageType
from database.connection import db_manager
from config.settings import settings


class SQLExpertAgent(BaseAgent):
    """SQL专家智能体，专门负责自然语言到SQL的转换"""

    def __init__(self):
        super().__init__(
            name="SQL专家", description="将自然语言查询转换为准确、安全的SQL语句"
        )

        # 初始化AutoGen助手智能体
        self.assistant = AssistantAgent(
            name="sql_expert",
            model_client=self._create_model_client(),
            system_message=self._get_sql_expert_prompt(),
        )

        # 数据库模式缓存
        self.schema_cache: Dict[str, List[Dict[str, Any]]] = {}

    def _create_model_client(self):
        """创建模型客户端"""
        from autogen_agentchat.models import OpenAIChatCompletionClient

        return OpenAIChatCompletionClient(
            model=settings.openai.model,
            api_key=settings.openai.api_key,
            base_url=settings.openai.base_url,
        )

    def _get_sql_expert_prompt(self) -> str:
        """获取SQL专家的系统提示词"""
        return """
你是一个专业的SQL专家，擅长将自然语言查询转换为准确的MySQL SQL语句。

核心职责：
1. 理解用户的自然语言查询意图
2. 根据数据库模式生成准确的SQL语句
3. 确保SQL语句的语法正确性和逻辑合理性
4. 优化查询性能
5. 只生成SELECT、INSERT、UPDATE、DELETE类型的语句

重要规则：
- 严格按照提供的数据库模式生成SQL
- 使用参数化查询防止SQL注入
- 对于模糊查询使用LIKE操作符
- 合理使用索引和JOIN优化性能
- 返回结果必须是有效的JSON格式

响应格式：
{
    "sql": "生成的SQL语句",
    "explanation": "SQL语句的解释",
    "parameters": ["参数列表"],
    "estimated_rows": "预估返回行数",
    "confidence": "置信度(0-1)"
}

如果无法生成SQL，返回：
{
    "error": "错误描述",
    "suggestions": ["改进建议"]
}
"""

    async def process_message(self, message: AgentMessage) -> AgentMessage:
        """处理自然语言查询请求"""
        try:
            if message.type != MessageType.QUERY:
                return self.create_response(
                    MessageType.ERROR, {"error": "SQL专家只处理查询类型的消息"}
                )

            query = message.content
            metadata = message.metadata or {}

            # 获取数据库模式信息
            schema_info = await self._get_database_schema()

            # 构建完整的提示词
            full_prompt = self._build_query_prompt(query, schema_info, metadata)

            # 调用AutoGen助手生成SQL
            response = await self.assistant.on_messages(
                [TextMessage(content=full_prompt, source="user")],
                cancellation_token=None,
            )

            # 解析响应
            sql_result = self._parse_sql_response(response.chat_message.content)

            return self.create_response(
                MessageType.SQL, sql_result, {"original_query": query}
            )

        except Exception as e:
            return self.log_error(e, "处理SQL转换请求时")

    async def _get_database_schema(self) -> Dict[str, Any]:
        """获取数据库模式信息"""
        try:
            # 获取所有表名
            tables = await db_manager.get_all_tables()

            schema_info = {"database": settings.database.database, "tables": {}}

            # 获取每个表的结构
            for table in tables:
                if table not in self.schema_cache:
                    self.schema_cache[table] = await db_manager.get_table_schema(table)

                schema_info["tables"][table] = self.schema_cache[table]

            return schema_info

        except Exception as e:
            logger.error(f"获取数据库模式失败: {e}")
            return {"database": settings.database.database, "tables": {}}

    def _build_query_prompt(
        self, query: str, schema_info: Dict[str, Any], metadata: Dict[str, Any]
    ) -> str:
        """构建查询提示词"""
        schema_text = self._format_schema_info(schema_info)

        prompt = f"""
用户查询：{query}

数据库模式信息：
{schema_text}

请根据上述数据库模式，将用户的自然语言查询转换为准确的MySQL SQL语句。

额外要求：
- 如果查询涉及时间范围，请考虑使用适当的日期函数
- 对于聚合查询，请使用适当的GROUP BY子句
- 对于排序需求，请添加ORDER BY子句
- 限制返回结果数量时使用LIMIT子句

请严格按照指定的JSON格式返回结果。
"""
        return prompt

    def _format_schema_info(self, schema_info: Dict[str, Any]) -> str:
        """格式化数据库模式信息"""
        if not schema_info.get("tables"):
            return "数据库模式信息不可用"

        formatted = f"数据库: {schema_info['database']}\n\n"

        for table_name, columns in schema_info["tables"].items():
            formatted += f"表: {table_name}\n"
            for col in columns:
                nullable = "NULL" if col["IS_NULLABLE"] == "YES" else "NOT NULL"
                default = (
                    f" DEFAULT {col['COLUMN_DEFAULT']}" if col["COLUMN_DEFAULT"] else ""
                )
                comment = (
                    f" -- {col['COLUMN_COMMENT']}" if col["COLUMN_COMMENT"] else ""
                )
                formatted += f"  - {col['COLUMN_NAME']}: {col['DATA_TYPE']} {nullable}{default}{comment}\n"
            formatted += "\n"

        return formatted

    def _parse_sql_response(self, response_content: str) -> Dict[str, Any]:
        """解析SQL响应内容"""
        try:
            # 尝试解析JSON响应
            if response_content.strip().startswith("{"):
                return json.loads(response_content)

            # 如果不是JSON格式，尝试提取SQL语句
            lines = response_content.strip().split("\n")
            for line in lines:
                if (
                    line.strip()
                    .upper()
                    .startswith(("SELECT", "INSERT", "UPDATE", "DELETE"))
                ):
                    return {
                        "sql": line.strip(),
                        "explanation": "从响应中提取的SQL语句",
                        "parameters": [],
                        "estimated_rows": "未知",
                        "confidence": 0.7,
                    }

            return {
                "error": "无法从响应中提取有效的SQL语句",
                "raw_response": response_content,
            }

        except json.JSONDecodeError as e:
            return {
                "error": f"JSON解析失败: {str(e)}",
                "raw_response": response_content,
            }


# 全局SQL专家智能体实例
sql_expert = SQLExpertAgent()
