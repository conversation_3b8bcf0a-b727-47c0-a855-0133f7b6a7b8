# AI 智能体文本转 SQL 系统

🤖 基于 AutoGen 0.6 框架的多智能体协作系统，能够将自然语言查询转换为 MySQL SQL 语句并自动执行。

## ✨ 主要特性

- 🧠 **智能理解**：支持中文自然语言查询，准确理解用户意图
- 🤝 **多智能体协作**：基于 AutoGen 0.6 的专业智能体分工合作
- 🔒 **安全防护**：内置 SQL 注入检测和多层安全验证机制
- ⚡ **自动执行**：安全执行生成的 SQL 查询并返回格式化结果
- 📊 **多格式展示**：支持表格、JSON、CSV 等多种结果展示格式
- 🛡️ **错误处理**：完善的错误处理和恢复机制

## 🏗️ 系统架构

### 智能体角色设计

```
用户查询 → 协调者智能体 → SQL专家智能体 → 安全审查智能体 → 数据库执行智能体 → 结果格式化智能体 → 用户
```

1. **SQL 专家智能体**：将自然语言转换为 SQL 语句
2. **安全审查智能体**：SQL 注入检测和安全验证
3. **数据库执行智能体**：安全执行 SQL 并处理结果
4. **结果格式化智能体**：美化结果展示
5. **协调者智能体**：管理整个工作流程

## 🚀 快速开始

### 环境要求

- Python 3.8+
- MySQL 8.4+
- OpenAI API Key

### 安装步骤

1. **克隆项目**

```bash
git clone <repository-url>
cd ai-agent-textToSQL1
```

2. **安装依赖**

```bash
pip install -r requirements.txt
```

3. **配置环境**

```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库和OpenAI API
```

4. **测试配置**

```bash
# 测试本地模型配置
python3 test_local_model.py
```

5. **运行系统**

```bash
# 交互模式
python3 main.py interactive

# 演示模式
python3 main.py demo
```

## 📝 使用示例

### 交互模式示例

```bash
$ python3 main.py interactive

🤖 AI智能体文本转SQL系统已启动
💡 输入自然语言查询，系统将自动转换为SQL并执行
💡 输入 'status' 查看系统状态
💡 输入 'quit' 或 'exit' 退出系统
------------------------------------------------------------

🔍 请输入查询: 查询所有用户信息

⏳ 正在处理查询...

✅ 查询执行成功

📊 结果统计:
   • 返回行数: 150
   • 列数: 5
   • 执行时间: 45.2 ms

📋 数据预览 (前 10 行):

+----+--------+-----+------------------+---------------------+
| id | name   | age | email            | created_at          |
+====+========+=====+==================+=====================+
|  1 | 张三   |  25 | <EMAIL>  | 2023-01-15 10:30:00 |
|  2 | 李四   |  30 | <EMAIL>     | 2023-01-16 14:20:00 |
+----+--------+-----+------------------+---------------------+

✅ 查询处理成功

🔍 查询内容: 查询所有用户信息
📊 结果统计: 成功返回 150 行数据
⏱️  总耗时: 156 ms

各步骤耗时:
  ✅ SQL专家: 89 ms
  ✅ 安全审查员: 12 ms
  ✅ 数据库执行器: 45 ms
  ✅ 结果格式化器: 10 ms
```

### 编程接口示例

```python
from main import TextToSQLSystem
import asyncio

async def main():
    # 创建系统实例
    system = TextToSQLSystem()

    # 初始化系统
    await system.initialize()

    # 处理查询
    result = await system.process_query("统计每个部门的员工数量")

    if result["success"]:
        print(result["final_result"]["formatted_output"])
    else:
        print(f"查询失败: {result['error']}")

asyncio.run(main())
```

## 🎯 支持的查询类型

### 基础查询

- "查询所有用户信息"
- "显示前 10 个订单"
- "查找名字叫张三的用户"

### 聚合查询

- "统计每个部门的员工数量"
- "计算平均工资"
- "查询最高销售额"

### 复杂查询

- "查询最近一周的订单，按金额排序"
- "统计每个月的销售趋势"
- "查找工资大于平均工资的员工"

## 🔒 安全特性

### 多层安全防护

1. **SQL 注入防护**

   - 危险关键词检测
   - 注入模式识别
   - 参数化查询支持

2. **操作限制**

   - 可配置的允许操作类型
   - 查询复杂度限制
   - 执行超时控制

3. **审计日志**
   - 完整的操作记录
   - 安全事件追踪
   - 性能监控

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest test/ -v

# 运行特定测试
pytest test/test_security.py -v
pytest test/test_agents.py -v
pytest test/test_integration.py -v

# 生成覆盖率报告
pytest test/ --cov=. --cov-report=html
```

### 测试覆盖

- ✅ 单元测试：智能体功能测试
- ✅ 安全测试：SQL 注入防护测试
- ✅ 集成测试：端到端功能测试
- ✅ 性能测试：并发和负载测试

## 📚 文档

- [技术架构文档](./docs/architecture.md) - 详细的系统架构说明
- [API 参考文档](./docs/api_reference.md) - 完整的 API 接口文档
- [配置说明](./docs/configuration.md) - 详细的配置选项说明
- [故障排除指南](./docs/troubleshooting.md) - 常见问题和解决方案

## 🔧 配置示例

### 基础配置 (.env)

```bash
# OpenAI配置 (支持官方API和本地模型)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_BASE_URL=https://api.openai.com/v1

# 本地模型配置示例
# OPENAI_BASE_URL=https://muses.weizhipin.com/muses-open/openai/v1
# OPENAI_API_KEY=your_local_api_key
# OPENAI_MODEL=deepseek-v3-0324

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_database_name

# 安全配置
MAX_QUERY_TIMEOUT=30
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE
ENABLE_SQL_INJECTION_CHECK=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 📊 项目结构

```
ai-agent-textToSQL1/
├── agents/                 # 智能体模块
│   ├── base_agent.py      # 基础智能体类
│   ├── sql_expert_agent.py # SQL专家智能体
│   ├── security_agent.py   # 安全审查智能体
│   ├── executor_agent.py   # 数据库执行智能体
│   ├── formatter_agent.py  # 结果格式化智能体
│   └── coordinator_agent.py # 协调者智能体
├── config/                 # 配置模块
│   └── settings.py        # 配置管理
├── database/              # 数据库模块
│   ├── connection.py      # 数据库连接管理
│   └── security.py        # SQL安全验证
├── utils/                 # 工具模块
│   ├── logger.py          # 日志配置
│   └── error_handler.py   # 错误处理
├── test/                  # 测试模块
│   ├── test_security.py   # 安全测试
│   ├── test_agents.py     # 智能体测试
│   └── test_integration.py # 集成测试
├── docs/                  # 文档目录
│   ├── README.md          # 项目说明
│   ├── architecture.md    # 架构文档
│   ├── api_reference.md   # API文档
│   ├── configuration.md   # 配置说明
│   └── troubleshooting.md # 故障排除
├── main.py                # 主程序入口
├── requirements.txt       # 依赖包列表
└── .env.example          # 配置模板
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [AutoGen](https://github.com/microsoft/autogen) - 多智能体框架
- [OpenAI](https://openai.com/) - AI 模型支持
- [MySQL](https://www.mysql.com/) - 数据库支持

## 📞 支持

如有问题或建议，请：

- 📧 发送邮件至：<EMAIL>
- 🐛 提交 [Issue](https://github.com/your-repo/issues)
- 💬 参与 [讨论](https://github.com/your-repo/discussions)

---

**⚠️ 注意**：本系统仅用于学习和研究目的，生产环境使用请确保充分测试和安全评估。
