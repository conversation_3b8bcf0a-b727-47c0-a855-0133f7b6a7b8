"""
应用配置管理模块
使用Pydantic进行配置验证和管理
"""

from typing import List, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """数据库配置"""

    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=3306, env="DB_PORT")
    user: str = Field(default="your_db_user", env="DB_USER")
    password: str = Field(default="your_db_password", env="DB_PASSWORD")
    database: str = Field(default="your_database_name", env="DB_NAME")

    # 连接池配置
    pool_size: int = Field(default=10, env="DB_POOL_SIZE")
    max_overflow: int = Field(default=20, env="DB_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")

    model_config = {"env_file": ".env", "extra": "ignore"}


class OpenAISettings(BaseSettings):
    """OpenAI API配置"""

    api_key: str = Field(default="your_openai_api_key_here", env="OPENAI_API_KEY")
    model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")

    model_config = {"env_file": ".env", "extra": "ignore"}


class SecuritySettings(BaseSettings):
    """安全配置"""

    max_query_timeout: int = Field(default=30, env="MAX_QUERY_TIMEOUT")
    allowed_operations: str = Field(
        default="SELECT,INSERT,UPDATE,DELETE", env="ALLOWED_OPERATIONS"
    )
    enable_sql_injection_check: bool = Field(
        default=True, env="ENABLE_SQL_INJECTION_CHECK"
    )

    model_config = {"env_file": ".env", "extra": "ignore"}

    def get_allowed_operations_list(self) -> List[str]:
        """获取允许操作的列表"""
        return [op.strip() for op in self.allowed_operations.split(",")]


class LogSettings(BaseSettings):
    """日志配置"""

    level: str = Field(default="INFO", env="LOG_LEVEL")
    file: str = Field(default="logs/app.log", env="LOG_FILE")

    model_config = {"env_file": ".env", "extra": "ignore"}


class AutoGenSettings(BaseSettings):
    """AutoGen配置"""

    cache_seed: int = Field(default=42, env="AUTOGEN_CACHE_SEED")
    timeout: int = Field(default=60, env="AUTOGEN_TIMEOUT")

    model_config = {"env_file": ".env", "extra": "ignore"}


class AppSettings:
    """应用主配置"""

    def __init__(self):
        self.database = DatabaseSettings()
        self.openai = OpenAISettings()
        self.security = SecuritySettings()
        self.logging = LogSettings()
        self.autogen = AutoGenSettings()


# 全局配置实例
settings = AppSettings()
