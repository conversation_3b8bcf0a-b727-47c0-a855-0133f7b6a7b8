# 配置说明文档

## 概述

本文档详细说明了 AI 智能体文本转 SQL 系统的配置选项和使用方法。

## 配置文件

### 环境变量文件 (.env)

系统使用`.env`文件管理配置，请复制`.env.example`并重命名为`.env`：

```bash
cp .env.example .env
```

### 配置结构

配置分为以下几个部分：

## 1. OpenAI API 配置

### 必需配置

```bash
# OpenAI API密钥
OPENAI_API_KEY=your_openai_api_key_here

# 使用的模型
OPENAI_MODEL=gpt-4

# API基础URL (可选，默认为OpenAI官方API)
OPENAI_BASE_URL=https://api.openai.com/v1
```

### 配置说明

- `OPENAI_API_KEY`: API 密钥，必须配置
- `OPENAI_MODEL`: 使用的 AI 模型，支持 OpenAI 和兼容模型
- `OPENAI_BASE_URL`: API 基础 URL，支持自定义和本地部署的模型

### 官方 OpenAI 配置

1. 访问 [OpenAI 官网](https://platform.openai.com/)
2. 注册并登录账户
3. 进入 API Keys 页面
4. 创建新的 API 密钥
5. 复制密钥到配置文件

```bash
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_BASE_URL=https://api.openai.com/v1
```

### 本地模型配置

系统支持任何 OpenAI 兼容的 API，包括本地部署的模型：

```bash
# 示例：本地部署的模型
OPENAI_BASE_URL=https://muses.weizhipin.com/muses-open/openai/v1
OPENAI_API_KEY=your_local_api_key
OPENAI_MODEL=deepseek-v3-0324
```

### 其他兼容 API

```bash
# Azure OpenAI
OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment
OPENAI_API_KEY=your_azure_api_key
OPENAI_MODEL=gpt-4

# 其他兼容服务
OPENAI_BASE_URL=https://your-custom-api.com/v1
OPENAI_API_KEY=your_custom_api_key
OPENAI_MODEL=your-model-name
```

## 2. MySQL 数据库配置

### 基础配置

```bash
# 数据库主机
DB_HOST=localhost

# 数据库端口
DB_PORT=3306

# 数据库用户名
DB_USER=your_db_user

# 数据库密码
DB_PASSWORD=your_db_password

# 数据库名称
DB_NAME=your_database_name
```

### 连接池配置

```bash
# 连接池大小
DB_POOL_SIZE=10

# 最大溢出连接数
DB_MAX_OVERFLOW=20

# 连接池超时时间(秒)
DB_POOL_TIMEOUT=30
```

### 配置说明

- `DB_HOST`: 数据库服务器地址
- `DB_PORT`: 数据库端口，MySQL 默认 3306
- `DB_USER`: 数据库用户名
- `DB_PASSWORD`: 数据库密码
- `DB_NAME`: 要连接的数据库名称
- `DB_POOL_SIZE`: 连接池中保持的连接数
- `DB_MAX_OVERFLOW`: 超出池大小的最大连接数
- `DB_POOL_TIMEOUT`: 获取连接的超时时间

### 数据库权限要求

确保数据库用户具有以下权限：

```sql
-- 基本查询权限
GRANT SELECT ON your_database_name.* TO 'your_db_user'@'localhost';

-- 如果需要修改数据
GRANT INSERT, UPDATE, DELETE ON your_database_name.* TO 'your_db_user'@'localhost';

-- 查看表结构权限
GRANT SHOW VIEW ON your_database_name.* TO 'your_db_user'@'localhost';
```

## 3. 安全配置

### 基础安全设置

```bash
# 查询超时时间(秒)
MAX_QUERY_TIMEOUT=30

# 允许的SQL操作类型(逗号分隔)
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE

# 是否启用SQL注入检查
ENABLE_SQL_INJECTION_CHECK=true
```

### 配置说明

- `MAX_QUERY_TIMEOUT`: 单个查询的最大执行时间
- `ALLOWED_OPERATIONS`: 允许执行的 SQL 操作类型
- `ENABLE_SQL_INJECTION_CHECK`: 是否启用 SQL 注入检测

### 安全级别配置

#### 高安全级别（生产环境推荐）

```bash
MAX_QUERY_TIMEOUT=15
ALLOWED_OPERATIONS=SELECT
ENABLE_SQL_INJECTION_CHECK=true
```

#### 中等安全级别（开发环境）

```bash
MAX_QUERY_TIMEOUT=30
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE
ENABLE_SQL_INJECTION_CHECK=true
```

#### 低安全级别（测试环境）

```bash
MAX_QUERY_TIMEOUT=60
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE,CREATE,DROP
ENABLE_SQL_INJECTION_CHECK=false
```

## 4. 日志配置

### 日志设置

```bash
# 日志级别
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=logs/app.log
```

### 日志级别说明

- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息（推荐）
- `WARNING`: 警告信息
- `ERROR`: 错误信息

### 日志文件管理

系统自动管理日志文件：

- 自动轮转：文件大小超过 10MB 时自动轮转
- 保留期：保留 7 天的日志文件
- 压缩：旧日志文件自动压缩
- 错误日志：单独的错误日志文件

## 5. AutoGen 配置

### AutoGen 设置

```bash
# 缓存种子
AUTOGEN_CACHE_SEED=42

# 超时时间(秒)
AUTOGEN_TIMEOUT=60
```

### 配置说明

- `AUTOGEN_CACHE_SEED`: 用于缓存的随机种子
- `AUTOGEN_TIMEOUT`: AutoGen 操作的超时时间

## 6. 高级配置

### 性能调优配置

```python
# config/settings.py 中的高级配置

class DatabaseSettings(PydanticBaseSettings):
    # 连接池配置
    pool_size: int = 10              # 连接池大小
    max_overflow: int = 20           # 最大溢出连接
    pool_timeout: int = 30           # 连接超时
    pool_recycle: int = 3600         # 连接回收时间

    # 查询配置
    query_timeout: int = 30          # 查询超时
    max_result_rows: int = 1000      # 最大结果行数

    # 缓存配置
    schema_cache_ttl: int = 3600     # 模式缓存TTL
```

### 智能体配置

```python
class AgentSettings(PydanticBaseSettings):
    # SQL专家配置
    sql_expert_temperature: float = 0.1    # 生成温度
    sql_expert_max_tokens: int = 1000      # 最大令牌数

    # 安全配置
    security_strict_mode: bool = True      # 严格模式
    security_whitelist: List[str] = []     # 白名单表

    # 格式化配置
    formatter_max_rows: int = 100          # 最大显示行数
    formatter_table_style: str = "grid"   # 表格样式
```

## 7. 环境特定配置

### 开发环境配置

```bash
# .env.development
OPENAI_MODEL=gpt-3.5-turbo
LOG_LEVEL=DEBUG
MAX_QUERY_TIMEOUT=60
ENABLE_SQL_INJECTION_CHECK=true
DB_POOL_SIZE=5
```

### 生产环境配置

```bash
# .env.production
OPENAI_MODEL=gpt-4
LOG_LEVEL=INFO
MAX_QUERY_TIMEOUT=15
ENABLE_SQL_INJECTION_CHECK=true
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=50
```

### 测试环境配置

```bash
# .env.test
OPENAI_MODEL=gpt-3.5-turbo
LOG_LEVEL=WARNING
MAX_QUERY_TIMEOUT=30
ENABLE_SQL_INJECTION_CHECK=false
DB_NAME=test_database
```

## 8. 配置验证

### 自动验证

系统启动时会自动验证配置：

```python
from config.settings import settings

# 验证数据库配置
assert settings.database.host, "数据库主机不能为空"
assert settings.database.user, "数据库用户不能为空"

# 验证OpenAI配置
assert settings.openai.api_key, "OpenAI API密钥不能为空"

# 验证安全配置
assert settings.security.max_query_timeout > 0, "查询超时必须大于0"
```

### 手动验证

```python
# 验证配置脚本
python3 -c "
from config.settings import settings
print('配置验证通过')
print(f'数据库: {settings.database.host}:{settings.database.port}')
print(f'模型: {settings.openai.model}')
"
```

## 9. 配置最佳实践

### 安全最佳实践

1. **敏感信息保护**

   - 不要将`.env`文件提交到版本控制
   - 使用强密码和复杂的 API 密钥
   - 定期轮换密钥和密码

2. **权限最小化**

   - 数据库用户只授予必要权限
   - 限制允许的 SQL 操作类型
   - 启用所有安全检查

3. **网络安全**
   - 使用 SSL 连接数据库
   - 限制数据库访问 IP
   - 使用防火墙保护

### 性能最佳实践

1. **连接池优化**

   - 根据并发需求调整池大小
   - 监控连接使用情况
   - 设置合适的超时时间

2. **查询优化**

   - 限制结果集大小
   - 设置合理的超时时间
   - 启用查询缓存

3. **资源管理**
   - 监控内存使用
   - 定期清理日志文件
   - 优化模型调用频率

### 监控配置

```bash
# 监控相关配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
```

## 10. 故障排除

### 常见配置问题

1. **数据库连接失败**

   ```bash
   # 检查配置
   echo $DB_HOST $DB_PORT $DB_USER $DB_NAME

   # 测试连接
   mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p $DB_NAME
   ```

2. **OpenAI API 错误**

   ```bash
   # 验证API密钥
   curl -H "Authorization: Bearer $OPENAI_API_KEY" \
        https://api.openai.com/v1/models
   ```

3. **权限问题**
   ```sql
   -- 检查用户权限
   SHOW GRANTS FOR 'your_db_user'@'localhost';
   ```

### 配置调试

```python
# 调试配置加载
import os
from config.settings import settings

print("环境变量:")
for key, value in os.environ.items():
    if key.startswith(('DB_', 'OPENAI_', 'LOG_')):
        print(f"{key}={value}")

print("\n解析后的配置:")
print(f"数据库: {settings.database.host}:{settings.database.port}")
print(f"模型: {settings.openai.model}")
print(f"日志级别: {settings.logging.level}")
```

## 11. 配置模板

### 完整配置模板

```bash
# OpenAI配置
OPENAI_API_KEY=sk-your-api-key-here
OPENAI_MODEL=gpt-4

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=sql_agent_user
DB_PASSWORD=secure_password_123
DB_NAME=business_db

# 连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# 安全配置
MAX_QUERY_TIMEOUT=30
ALLOWED_OPERATIONS=SELECT,INSERT,UPDATE,DELETE
ENABLE_SQL_INJECTION_CHECK=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# AutoGen配置
AUTOGEN_CACHE_SEED=42
AUTOGEN_TIMEOUT=60
```

将此模板保存为`.env`文件，并根据实际环境修改相应的值。
