# 本地模型配置指南

## 概述

本系统支持使用本地部署的AI模型，而不仅限于OpenAI官方API。只要模型服务提供OpenAI兼容的API接口，就可以无缝集成到系统中。

## 支持的模型类型

### 1. 本地部署模型
- **DeepSeek系列**: deepseek-v3-0324, deepseek-coder等
- **Qwen系列**: qwen-turbo, qwen-plus等  
- **ChatGLM系列**: chatglm3-6b, chatglm4等
- **Llama系列**: llama2-7b, llama2-13b等
- **其他开源模型**: 通过vLLM、FastChat等框架部署

### 2. 云服务API
- **Azure OpenAI**: 微软Azure平台的OpenAI服务
- **阿里云通义千问**: 阿里云的大模型服务
- **腾讯云混元**: 腾讯云的大模型服务
- **百度文心一言**: 百度的大模型服务

## 配置步骤

### 1. 基础配置

编辑`.env`文件，设置以下参数：

```bash
# 本地模型配置
OPENAI_BASE_URL=https://your-local-api-endpoint/v1
OPENAI_API_KEY=your_local_api_key
OPENAI_MODEL=your_model_name
```

### 2. 具体配置示例

#### DeepSeek模型配置
```bash
OPENAI_BASE_URL=https://muses.weizhipin.com/muses-open/openai/v1
OPENAI_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.ED2fuIwehJ6fbi9PWGY7jgGCOKDGIJ19BbN4YEZHCjE
OPENAI_MODEL=deepseek-v3-0324
```

#### vLLM本地部署
```bash
# 假设在本地8000端口运行vLLM服务
OPENAI_BASE_URL=http://localhost:8000/v1
OPENAI_API_KEY=EMPTY
OPENAI_MODEL=your-local-model
```

#### Ollama本地部署
```bash
# Ollama通常在11434端口运行
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_API_KEY=ollama
OPENAI_MODEL=llama2:7b
```

#### Azure OpenAI
```bash
OPENAI_BASE_URL=https://your-resource.openai.azure.com/openai/deployments/your-deployment
OPENAI_API_KEY=your_azure_api_key
OPENAI_MODEL=gpt-4
```

## 测试配置

### 1. 运行配置测试

```bash
python3 test_local_model.py
```

这个脚本会测试：
- 配置文件加载
- 模型客户端创建
- 基础连接测试

### 2. 预期输出

```
🚀 开始本地模型配置测试

⚙️ 测试配置加载...
✅ OpenAI配置:
   - API Key: 已设置
   - 模型: deepseek-v3-0324
   - Base URL: https://muses.weizhipin.com/muses-open/openai/v1

🔧 测试本地模型配置...
📍 Base URL: https://muses.weizhipin.com/muses-open/openai/v1
🤖 模型: deepseek-v3-0324
🔑 API Key: eyJhbGciOiJIUzI1NiIsInR5...
✅ SQL专家智能体创建成功
✅ 模型客户端创建成功

==================================================
📊 测试结果汇总:
==================================================
配置加载: ✅ 通过
本地模型连接: ✅ 通过
简单查询: ✅ 通过

总计: 3/3 测试通过
🎉 所有测试通过！本地模型配置正常。
```

## 常见问题

### 1. 连接超时

**问题**: 连接本地模型服务超时

**解决方案**:
```bash
# 增加超时时间
AUTOGEN_TIMEOUT=120

# 检查网络连接
curl -X POST https://your-api-endpoint/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_api_key" \
  -d '{"model": "your_model", "messages": [{"role": "user", "content": "Hello"}]}'
```

### 2. API密钥错误

**问题**: 401 Unauthorized错误

**解决方案**:
- 检查API密钥是否正确
- 确认密钥格式符合要求
- 验证密钥是否有效期内

### 3. 模型名称错误

**问题**: 模型不存在或不可用

**解决方案**:
```bash
# 查询可用模型列表
curl https://your-api-endpoint/v1/models \
  -H "Authorization: Bearer your_api_key"
```

### 4. 响应格式不兼容

**问题**: 模型响应格式与OpenAI不兼容

**解决方案**:
- 确认API服务完全兼容OpenAI格式
- 检查响应字段是否完整
- 联系API服务提供商确认兼容性

## 性能优化

### 1. 模型选择建议

- **SQL生成任务**: 推荐使用代码能力强的模型（如DeepSeek-Coder、CodeLlama）
- **自然语言理解**: 推荐使用通用对话模型（如ChatGLM、Qwen）
- **平衡性能和成本**: 根据实际需求选择合适大小的模型

### 2. 参数调优

```bash
# 降低生成温度，提高确定性
OPENAI_TEMPERATURE=0.1

# 限制最大令牌数
OPENAI_MAX_TOKENS=1000

# 设置合理的超时时间
AUTOGEN_TIMEOUT=60
```

### 3. 缓存策略

```bash
# 启用结果缓存
ENABLE_CACHE=true
CACHE_TTL=3600

# 启用模式缓存
SCHEMA_CACHE_TTL=7200
```

## 部署建议

### 1. 本地开发环境

```bash
# 使用轻量级模型进行开发测试
OPENAI_MODEL=qwen-7b-chat
OPENAI_BASE_URL=http://localhost:8000/v1
```

### 2. 生产环境

```bash
# 使用性能更强的模型
OPENAI_MODEL=deepseek-v3-0324
OPENAI_BASE_URL=https://your-production-api/v1

# 增加安全配置
ENABLE_SSL_VERIFY=true
API_RATE_LIMIT=100
```

### 3. 高可用配置

```bash
# 配置多个API端点进行负载均衡
PRIMARY_API_URL=https://api1.example.com/v1
BACKUP_API_URL=https://api2.example.com/v1

# 启用自动重试
MAX_RETRIES=3
RETRY_DELAY=1
```

## 监控和日志

### 1. 启用详细日志

```bash
LOG_LEVEL=DEBUG
LOG_FILE=logs/local_model.log
```

### 2. 监控指标

- API响应时间
- 请求成功率
- 模型推理耗时
- 错误率统计

### 3. 告警配置

```bash
# 设置响应时间阈值
RESPONSE_TIME_THRESHOLD=5000

# 设置错误率阈值
ERROR_RATE_THRESHOLD=0.1
```

## 安全考虑

### 1. API密钥管理

- 定期轮换API密钥
- 使用环境变量存储敏感信息
- 避免在代码中硬编码密钥

### 2. 网络安全

- 使用HTTPS加密传输
- 配置防火墙规则
- 限制API访问IP范围

### 3. 数据隐私

- 确保本地模型不会泄露敏感数据
- 配置数据脱敏规则
- 定期审计访问日志

## 故障排除

### 1. 连接问题诊断

```bash
# 测试网络连通性
ping your-api-host

# 测试端口可达性
telnet your-api-host 443

# 测试API可用性
curl -I https://your-api-endpoint/v1/models
```

### 2. 日志分析

```bash
# 查看错误日志
tail -f logs/error.log | grep -i "model\|api"

# 分析响应时间
grep "response_time" logs/app.log | tail -20
```

### 3. 性能分析

```bash
# 监控系统资源
htop

# 分析网络延迟
traceroute your-api-host
```

通过以上配置和优化，您可以成功将本地模型集成到AI智能体文本转SQL系统中，享受更好的性能、隐私保护和成本控制。
