#!/usr/bin/env python3
"""
测试本地模型连接
验证本地OpenAI兼容API是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from config.settings import settings
from agents.sql_expert_agent import SQLExpertAgent
from agents.base_agent import AgentMessage, MessageType


async def test_local_model_connection():
    """测试本地模型连接"""
    print("🔧 测试本地模型配置...")
    print(f"📍 Base URL: {settings.openai.base_url}")
    print(f"🤖 模型: {settings.openai.model}")
    print(f"🔑 API Key: {settings.openai.api_key[:20]}...")
    
    try:
        # 创建SQL专家智能体
        sql_expert = SQLExpertAgent()
        print("✅ SQL专家智能体创建成功")
        
        # 测试模型客户端创建
        model_client = sql_expert._create_model_client()
        print("✅ 模型客户端创建成功")
        print(f"   模型: {model_client.model}")
        print(f"   Base URL: {model_client.base_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 本地模型配置测试失败: {e}")
        return False


async def test_simple_query():
    """测试简单查询处理"""
    print("\n🧪 测试简单查询处理...")
    
    try:
        # 创建测试消息
        test_message = AgentMessage(
            type=MessageType.QUERY,
            content="查询用户表的结构",
            metadata={"session_id": "test"},
            sender="test_user"
        )
        
        print(f"📝 测试查询: {test_message.content}")
        
        # 注意：这里只测试消息创建，不实际调用API
        # 因为可能没有真实的数据库连接
        print("✅ 查询消息创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询处理测试失败: {e}")
        return False


async def test_configuration():
    """测试配置加载"""
    print("\n⚙️ 测试配置加载...")
    
    try:
        print(f"✅ OpenAI配置:")
        print(f"   - API Key: {'已设置' if settings.openai.api_key else '未设置'}")
        print(f"   - 模型: {settings.openai.model}")
        print(f"   - Base URL: {settings.openai.base_url}")
        
        print(f"✅ 数据库配置:")
        print(f"   - 主机: {settings.database.host}")
        print(f"   - 端口: {settings.database.port}")
        print(f"   - 数据库: {settings.database.database}")
        
        print(f"✅ 安全配置:")
        print(f"   - 查询超时: {settings.security.max_query_timeout}秒")
        print(f"   - 允许操作: {settings.security.allowed_operations}")
        print(f"   - SQL注入检查: {'启用' if settings.security.enable_sql_injection_check else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始本地模型配置测试\n")
    
    tests = [
        ("配置加载", test_configuration),
        ("本地模型连接", test_local_model_connection),
        ("简单查询", test_simple_query),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！本地模型配置正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置。")
        return False


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        sys.exit(1)
